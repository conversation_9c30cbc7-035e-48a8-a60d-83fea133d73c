<script setup lang="ts">
import { docCookies } from "@/utils/cookieop"
import { userInfoStore } from "@/stores/userInfo"
import { ref, onMounted, defineProps, watch } from "vue"
import { routerPushSystem } from "@/utils/jump"
import { System } from "@/enums/system"
import { logoutApi } from "@/apis/path/user"
import { ElMessage } from "element-plus"

const userinfo = userInfoStore()

// 个人空间,跳转champagin
const perurl = import.meta.env.VITE_APP_CHAMP_URL + "/userspace"

const learningHistory = import.meta.env.VITE_APP_CHAMP_URL + '/learnStatistics'
// 账号资料,跳转youthfountain
const accurl = import.meta.env.VITE_APP_YOUTH_URL + "/userinfo"

let backurl = null

const backendManagement = ref(false)

const avatar = userinfo.getAvatar()
const permissions = userinfo.getPermission()
console.log("permissions",permissions);
const admin = permissions.find((system) => system.service === "admin")
if (admin && admin.access) {
  backendManagement.value = true
  console.log("admin",admin);
  backurl = admin.url
  console.log("backurl",backurl);
}

// 登出
const logout = async () => {
  try {
    const res = await logoutApi()
    if (res.success) {
      ElMessage.success("退出成功")
    }
  } catch (error) {
    console.error(error)
  }
}

// 路由跳转
const routerPush = (targetRoute: string) => {
  if (targetRoute === undefined) {
    alert("路由跳转失败")
  } else {
    setTimeout(() => {
      window.open(targetRoute, "_self")
    }, 0)
  }
}
</script>
<template>
  <div class="header">
    <span class="left-wrapper">
      <span class="logo">
        <img src="@/assets/image/header/logo.png" class="pic" />
      </span>
      <span class="title">
        <span>知识中心</span>
      </span>
      <span class="pusher">
        <span class="pusher-item" @click="routerPushSystem(System.champaign)">
          <img class="pusher-item-img" src="@/assets/image/header/study.svg" />
          <span>学习站</span>
        </span>
      </span>
    </span>
    <span class="avatar-wrapper">
      <el-dropdown popper-class="primary">
        <img class="avatar" :src="avatar" style="width: 35px" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="routerPush(perurl)"
              >个人空间</el-dropdown-item
            >
            <el-dropdown-item @click="routerPush(learningHistory)"
              >学习历史</el-dropdown-item
            >
            <el-dropdown-item @click="routerPush(accurl)"
              >账号资料</el-dropdown-item
            >
            <el-dropdown-item
              v-if="backendManagement"
              @click="routerPush(backurl)"
              >后台管理</el-dropdown-item
            >
            <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </span>
  </div>
</template>
<style scoped lang="less">
.header {
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  background-color: white;
  .left-wrapper {
    display: flex;
    align-items: center;
    width: max-content;
    height: 100%;
    font-family: var(--font-family-logo);
    color: var(--color-primary);
    .logo {
      width: 106px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      .pic {
        height: 60px;
      }
    }

    .title {
      font-weight: 600;
      color: var(--color-primary);
      font-size: 20px;

      // & > span {
      //   letter-spacing: 1px;
      //   font-weight: 600;
      // }
    }
    .pusher {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;
      justify-content: center;
      margin-left: 30px;
      .pusher-item {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-deep);
        font-size: 14px;
        margin-left: 40px;
        &:hover {
          cursor: pointer;
          font-weight: bold;
        }
        .pusher-item-img {
          margin-right: 5px;
        }
      }
    }
  }

  .avatar-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    font-family: var(--font-family-text);

    .avatar {
      margin-left: 79px;
      margin-right: 65px;
      border-radius: 10px;
      height: 35px;
      width: 35px;
      cursor: pointer;

      &:focus-visible {
        outline: none !important;
      }
    }
  }
}
</style>
